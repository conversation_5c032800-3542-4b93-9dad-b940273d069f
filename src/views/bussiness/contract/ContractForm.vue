<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="700px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="auto"
      v-loading="formLoading"
    >
      <h3>合同信息</h3>
      <el-row>
        <el-col :span="12">
          <el-form-item label="合同开始时间" prop="startTime">
            <el-date-picker
              v-model="formData.startTime"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="date"
              placeholder="选择合同开始时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同结束时间" prop="endTime">
            <el-date-picker
              v-model="formData.endTime"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="date"
              placeholder="选择合同结束时间"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="设备使用地点" prop="useAddress">
        <el-input v-model="formData.useAddress" placeholder="请输入设备安装使用地点"/>
      </el-form-item>
      <el-row>
        <el-row>
          <el-form-item label="签订日期" prop="signTime">
            <el-date-picker
              v-model="formData.signTime"
              type="date"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="选择签订日期"
            />
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="结算周期/月" prop="settlementCycle">
            <el-input
              type="number"
              min="1"
              v-model="formData.settlementCycle"
              placeholder="请输入结算周期/月"
              :disabled="formType === 'update'"
            />
          </el-form-item>
        </el-row>
      </el-row>
      <h3>乙方信息</h3>
      <el-row>
        <el-col :span="12">
          <el-form-item label="买方地址" prop="buyAddress">
            <el-input v-model="formData.buyAddress" placeholder="请输入买方地址"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="买方公司名" prop="buyName">
            <el-input v-model="formData.buyName" placeholder="请输入买方公司名称"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="买方邮编" prop="buyPostalCode">
            <el-input v-model="formData.buyPostalCode" placeholder="请输入邮政编码"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="买方手机号" prop="buyPhone">
            <el-input type="tel" v-model="formData.buyPhone" placeholder="请输入买方手机号"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="买方传真" prop="buyFox">
            <el-input v-model="formData.buyFox" placeholder="请输入传真"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="买方经办人" prop="buyAgent">
            <el-input v-model="formData.buyAgent" placeholder="请输入经办人姓名"/>
          </el-form-item>
        </el-col>
      </el-row>

      <h3>结算方式</h3>
      <el-row>
        <el-col :span="12">
        <el-form-item label="黑白免费张数" prop="blackWhiteFree">
          <el-input type="number" min="1" v-model="formData.blackWhiteFree"
                    placeholder="请输入黑白免费张数"/>
        </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="黑白超出租金" prop="blackWhiteMoney">
            <el-input-number
              v-model="formData.blackWhiteMoney"
              :min="0"
              :precision="2"
              controls-position="right"
              placeholder="请输入黑白超出租金"
              class="!w-100%"
            />
          </el-form-item>
        </el-col>
      </el-row>


      <el-row>
        <el-col :span="12">
          <el-form-item label="彩色免费张数" prop="colorFree">
            <el-input type="number" min="1" v-model="formData.colorFree"
                      placeholder="请输入彩色免费张数"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="彩色超出租金" prop="colorMoney">
            <el-input-number
              v-model="formData.colorMoney"
              :min="0"
              :precision="2"
              controls-position="right"
              placeholder="请输入彩色超出租金"
              class="!w-100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="每台机器押金" prop="pledgeMoney">
            <el-input type="number" min="1" v-model="formData.pledgeMoney"
                      placeholder="请输入每台机器押金"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="服务费" prop="monthlyMoney">
            <el-input type="number"  v-model="formData.monthlyMoney"
                      placeholder="请输入服务费"/>
          </el-form-item>
        </el-col>
      </el-row>

      <h3>打印机信息</h3>
      <el-row justify="end" class="mb-3">
        <el-button type="primary" @click="openPrinterForm">
          <Icon icon="ep:plus" class="mr-5px" />
          新增打印机
        </el-button>
      </el-row>

      <el-table :data="formData.printerSaveList" border>
        <el-table-column label="序号" type="index" align="center" width="60" />
        <el-table-column label="打印机名称" width="200">
          <template #default="{ row }">
            <el-input v-model="row.printerName" placeholder="请输入打印机名称" />
          </template>
        </el-table-column>
        <el-table-column label="黑白起始张数" width="150">
          <template #default="{ row }">
            <el-input-number
              v-model="row.blackWhiteNumber"
              :min="0"
              :max="999999"
              controls-position="right"
              class="!w-100%"
            />
          </template>
        </el-table-column>
        <el-table-column label="彩色起始张数" width="150">
          <template #default="{ row }">
            <el-input-number
              v-model="row.colorNumber"
              :min="0"
              :max="999999"
              controls-position="right"
              class="!w-100%"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="100">
          <template #default="{ row, $index }">
            <el-button link type="danger" @click="deletePrinter(row, $index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>

  <!-- 新增打印机弹窗 -->
  <Dialog :title="'新增打印机'" v-model="printerDialogVisible" width="500px">
    <el-form
      ref="printerFormRef"
      :model="printerFormData"
      :rules="printerFormRules"
      label-width="120px"
    >
      <el-form-item label="打印机名称" prop="printerName">
        <el-input v-model="printerFormData.printerName" placeholder="请输入打印机名称" />
      </el-form-item>
      <el-form-item label="黑白起始张数" prop="blackWhiteNumber">
        <el-input type="number" min="0" v-model="printerFormData.blackWhiteNumber" placeholder="请输入黑白数量" />
      </el-form-item>
      <el-form-item label="彩色起始张数" prop="colorNumber">
        <el-input type="number" min="0" v-model="printerFormData.colorNumber" placeholder="请输入黑白数量" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="addPrinter" type="primary">确 定</el-button>
      <el-button @click="printerDialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import {ContractApi, ContractVO} from '@/api/hzjc/contract'
import { ContractPrinterApi } from '@/api/hzjc/contractprinter'
import { nextTick } from 'vue'

/** 合同 表单 */
defineOptions({name: 'ContractForm'})

const {t} = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  // 合同信息
  contractNo: undefined,
  startTime: undefined,
  endTime: undefined,
  useAddress: undefined,
  signTime: undefined,
  //结算周期/月
  settlementCycle: undefined,
  //买方入参信息
  buyName: undefined,
  buyPostalCode: undefined,
  buyPhone: undefined,
  buyFox: undefined,
  buyAgent: undefined,
  buyAddress: undefined,
  //结算方式
  blackWhiteFree: undefined,
  blackWhiteMoney: undefined,
  colorFree: undefined,
  pledgeMoney: undefined,
  colorMoney: undefined,
  monthlyMoney: undefined,
  // 打印机信息
  printerSaveList: [
    {
      printerName: '',
      blackWhiteNumber: 0,
      colorNumber: 0
    }
  ]
})
const formRules = reactive({
  startTime: [{required: true, message: '合同开始时间不能为空', trigger: 'blur'}],
  endTime: [{required: true, message: '合同结束时间不能为空', trigger: 'blur'}],
  buyName: [{required: true, message: '买方名称不能为空', trigger: 'blur'}],
  buyPhone: [
    {required: false, message: '请输入手机号码', trigger: 'blur'},
    {pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur'}
  ]
})
const formRef = ref() // 表单 Ref

// 打印机弹窗相关
const printerDialogVisible = ref(false)
const printerFormRef = ref()
const printerFormData = ref({
  printerName: '',
  blackWhiteNumber: 0,
  colorNumber: 0
})
const printerFormRules = reactive({
  printerName: [{required: true, message: '打印机名称不能为空', trigger: 'blur'}],
})

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await ContractApi.getContract(id)
      formData.value = data
      // 将后端返回的 contractPrinterDOList 赋值给 printerSaveList 实现回显
      if (data.contractPrinterDOList && Array.isArray(data.contractPrinterDOList)) {
        formData.value.printerSaveList = data.contractPrinterDOList.map(printer => ({
          contractPrinterId: printer.contractPrinterId, // 修改时保留contractPrinterId
          printerName: printer.printerName,
          blackWhiteNumber: printer.blackWhiteNumber,
          colorNumber: printer.colorNumber
        }))
      } else {
        formData.value.printerSaveList = []
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({open}) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ContractVO
    if (formType.value === 'create') {
      await ContractApi.createContract(data)
      message.success(t('common.createSuccess'))
    } else {
      await ContractApi.updateContract(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    // 合同信息
    contractNo: undefined,
    startTime: undefined,
    endTime: undefined,
    useAddress: undefined,
    signTime: undefined,
    //结算周期/月
    settlementCycle: undefined,
    //买方入参信息
    buyName: undefined,
    buyPostalCode: undefined,
    buyPhone: undefined,
    buyFox: undefined,
    buyAgent: undefined,
    buyAddress: undefined,
    //结算方式
    blackWhiteFree: undefined,
    blackWhiteMoney: undefined,
    colorFree: undefined,
    pledgeMoney: undefined,
    colorMoney: undefined,
    monthlyMoney: undefined,
    // 打印机信息
    printerSaveList: []
  }
  formRef.value?.resetFields()
}

/** 打开打印机表单 */
const openPrinterForm = () => {
  printerDialogVisible.value = true
  printerFormData.value = {
    printerName: '',
    blackWhiteNumber: 0,
    colorNumber: 0
  }
  // 清空之前的验证状态
  nextTick(() => {
    printerFormRef.value?.clearValidate()
  })
}

/** 添加打印机 */
const addPrinter = async () => {
  try {
    await printerFormRef.value.validate()
    // 确保 printerSaveList 数组存在
    if (!formData.value.printerSaveList) {
      formData.value.printerSaveList = []
    }
    formData.value.printerSaveList.push({
      contractPrinterId: undefined, // 新增时为undefined
      ...printerFormData.value
    })
    printerDialogVisible.value = false
    message.success('打印机添加成功')
  } catch (error) {
    console.log('打印机表单验证失败:', error)
  }
}

/** 删除打印机 */
const deletePrinter = async (printer: any, index: number) => {
  try {
    // 如果有contractPrinterId，说明是已保存的打印机，需要调用删除接口
    if (printer.contractPrinterId) {
      // 删除的二次确认
      await message.delConfirm('确认删除该打印机吗？')
      // 调用打印机删除接口
      await ContractPrinterApi.deleteContractPrinter(printer.contractPrinterId)
      message.success('打印机删除成功')
    }
    // 从数组中移除
    formData.value.printerSaveList.splice(index, 1)
  } catch (error) {
    console.error('删除打印机失败:', error)
  }
}
</script>
