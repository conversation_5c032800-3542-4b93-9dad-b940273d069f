<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="合同编号" prop="contractNo">
        <el-input
          v-model="queryParams.contractNo"
          placeholder="请输入合同编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-140px"
        />
      </el-form-item>

      <el-form-item label="合同状态" prop="contractState">
        <el-select
          v-model="queryParams.contractState"
          placeholder="请选择合同状态"
          clearable
          class="!w-145px"
        >
          <el-option label="执行中" :value="1" />
          <el-option label="作废" :value="2" />
          <el-option label="已到期" :value="3" />
        </el-select>
      </el-form-item>

      <el-form-item label="公司名称" prop="buyName">
        <el-input
          v-model="queryParams.buyName"
          placeholder="请输入公司名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-140px"
        />
      </el-form-item>
      <el-form-item label="签订日期" prop="signTime">
        <el-date-picker
          v-model="queryParams.signTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>

      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['hzjc:contract:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['hzjc:contract:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="合同编号" align="center" prop="contractNo" width="160"/>
      <el-table-column label="合同状态" align="center" prop="contractState" width="140">
        <template #default="scope">
          <el-select
            v-model="scope.row.contractState"
            @change="handleStatusChange(scope.row)"
            size="small"
            style="width: 100px"
          >
            <el-option label="执行中" :value="1" />
            <el-option label="作废" :value="2" />
            <el-option label="已到期" :value="3" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="买方公司名称" align="center" prop="buyName" />
      <el-table-column
        label="合同开始时间"
        align="center"
        prop="startTime"
        :formatter="dateFormatter2"
        width="180px"
      />
      <el-table-column
        label="合同结束时间"
        align="center"
        prop="endTime"
        :formatter="dateFormatter2"
        width="180px"
      />
      <el-table-column label="设备安装使用地点" align="center" prop="useAddress" />
      <el-table-column
        label="签订日期"
        align="center"
        prop="signTime"
        :formatter="dateFormatter2"
        width="180px"
      />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />

      <el-table-column label="操作" align="center" width="290">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.contractId)"
            v-hasPermi="['hzjc:contract:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="success"
            @click="handleDownloadContract(scope.row.contractId)"
            v-hasPermi="['hzjc:contract:makeWord']"
          >
            下载合同
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.contractId)"
            v-hasPermi="['hzjc:contract:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ContractForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter,dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { ContractApi, ContractVO } from '@/api/hzjc/contract'
import ContractForm from './ContractForm.vue'

/** 合同 列表 */
defineOptions({ name: 'Contract' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<ContractVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  contractNo: undefined,
  buyName: undefined,
  signTime: [],
  contractState: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ContractApi.getContractPage(queryParams)

    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ContractApi.deleteContract(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ContractApi.exportContract(queryParams)
    download.excel(data, '合同.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 下载合同按钮操作 */
const handleDownloadContract = async (id: number) => {
  try {
    // 下载的二次确认
    await message.confirm('确定要下载该合同文档吗？')
    // 发起下载
    const data = await ContractApi.makeWord(id)
    console.log('发起下载data:', data)

    download.word(data, '合同.docx')
    message.success('合同下载成功')
  } catch (error) {
    console.error('下载合同失败:', error)
  }
}

/** 合同状态修改 */
const handleStatusChange = async (row: ContractVO) => {
  try {
    const statusText = row.contractState === 1 ? '执行中' :
                      row.contractState === 2 ? '作废' : '已到期'
    // 状态修改的二次确认
    await message.confirm(`确认要将合同状态修改为"${statusText}"吗？`)
    // 发起状态修改请求
    await ContractApi.updateContractState({
      contractId: row.contractId,
      contractState: row.contractState
    })
    message.success('合同状态修改成功')
    // 刷新列表
    await getList()
  } catch (error) {
    // 取消后恢复原状态，需要重新获取数据
    await getList()
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
