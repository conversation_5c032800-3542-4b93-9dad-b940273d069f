<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >


      <el-form-item label="合同状态" prop="contractState">
        <el-select
          v-model="queryParams.contractState"
          placeholder="请选择合同状态"
          clearable
          class="!w-90px"
        >
          <el-option label="执行中" :value="1" />
          <el-option label="已作废" :value="2" />
          <el-option label="已到期" :value="3" />
        </el-select>
      </el-form-item>

      <!-- 新增公司名称下拉搜索 -->
      <el-form-item label="公司名称" prop="contractBuyId">
        <el-select
            v-model="queryParams.contractBuyId"
            placeholder="请选择公司"
            class="!w-130px"
            filterable
            remote
            :remote-method="fetchCompanies"
            :loading="companyLoading"
            clearable
        >
          <el-option
              v-for="item in companyOptions"
              :key="item.contractBuyId"
              :label="item.buyName"
              :value="item.contractBuyId"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="是否缴费" prop="isPay">
        <el-select
          v-model="queryParams.isPay"
          placeholder="请选择是否缴费"
          clearable
          class="!w-150px"
        >
          <el-option label="已缴费" :value="true" />
          <el-option label="未缴费" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item label="应缴费时间" prop="payTime" style="white-space: nowrap;">
        <el-date-picker
          v-model="queryParams.payTime"
          value-format="YYYY-MM-01 00:00:00"
          type="month"
          placeholder="选择月份"
          class="!w-120px"
        />
      </el-form-item>



      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['hzjc:paylog:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="合同编号" align="center" prop="contractNo" width="150" />
      <el-table-column label="合同状态" align="center" prop="contractState" width="150">
        <template #default="scope">
          <el-tag
            :type="scope.row.contractState === 1 ? 'success' : scope.row.contractState === 2 ? 'danger' : 'warning'"
          >
            {{
              scope.row.contractState === 1 ? '执行中' :
                scope.row.contractState === 2 ? '作废' :
                  scope.row.contractState === 3 ? '已到期' : '未知'
            }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="应缴费时间" align="center" prop="payTime" :formatter="dateFormatter3" width="110px"/>
      <el-table-column label="公司名称" align="center" prop="buyName" width="150" />
      <el-table-column label="地址" align="center" prop="useAddress" width="200"/>
      <el-table-column label="是否缴费" align="center" prop="isPay" width="150" >
        <template #default="scope">
          <el-tag :type="scope.row.isPay ? 'success' : 'danger'">
            {{ scope.row.isPay ? '已缴费' : '未缴费' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="应缴费金额" align="center" prop="amount" width="120" />
      <el-table-column label="实际缴费金额" align="center" prop="actualAmount" width="120" />
      <el-table-column label="备注" align="center" prop="remark" width="150" />

      <el-table-column label="操作" align="center" width="180" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.payLogId)"
            v-hasPermi="['hzjc:paylog:query']"
          >
            缴费
          </el-button>

          <el-button
            link
            type="primary"
            @click="openRecordForm(scope.row)"
            v-hasPermi="['hzjc:printercount:create']"
          >
            记数
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.payLogId)"
            v-hasPermi="['hzjc:paylog:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ContractPayLogForm ref="formRef" @success="getList" />

  <!-- 记张弹窗 -->
  <Dialog :title="'计数'" v-model="recordDialogVisible" width="800px">
    <el-form
      ref="recordFormRef"
      :model="recordFormData"
      label-width="120px"
    >
      <!-- 打印机列表 -->
      <el-table :data="printerList" border style="width: 100%">
        <el-table-column label="打印机编号" prop="printeNo"  width="100" />
        <el-table-column label="打印机名称" prop="printerName" width="150"/>
        <el-table-column label="黑白张数" width="150">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.blackWhiteNumber"
              :min="0"
              :max="999999"
              :disabled="recordFormData.isPay"
              :precision="0"
              :step="1"
              :controls="true"
              size="small"
              @change="handleNumberChange(scope.row, 'blackWhiteNumber')"
            />
          </template>
        </el-table-column>
        <el-table-column label="彩色张数" width="150">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.colorNumber"
              :min="0"
              :max="999999"
              :disabled="recordFormData.isPay"
              :precision="0"
              :step="1"
              :controls="true"
              size="small"
              @change="handleNumberChange(scope.row, 'colorNumber')"
            />
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <template #footer>
      <el-button @click="recordDialogVisible = false">取 消</el-button>
      <el-button
        @click="submitRecord"
        type="primary"
        :loading="recordLoading"
        :disabled="recordFormData.isPay"
      >
        确 定
      </el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import {dateFormatter3 } from '@/utils/formatTime'
import download from '@/utils/download'
import { ContractPayLogApi, ContractPayLogVO } from '@/api/hzjc/contractpaylog'
import ContractPayLogForm from './ContractPayLogForm.vue'
import { ContractBuyApi } from '@/api/hzjc/contractbuy'
import { ContractPrinterApi } from '@/api/hzjc/contractprinter'
import { ContractPrinterCountApi } from '@/api/hzjc/contractprintercount'

/** 合同缴费记录 列表 */
defineOptions({ name: 'ContractPayLog' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<ContractPayLogVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  contractId: undefined,
  isPay: undefined,
  amount: undefined,
  actualAmount: undefined,
  contractBuyId: undefined,
  payTime: undefined,
  contractState: 1 // 默认查询执行中状态
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 定义公司选项的类型
interface CompanyOption {
  contractBuyId: number
  buyName: string
}

// 公司下拉相关变量
const companyOptions = ref<CompanyOption[]>([])
const companyLoading = ref(false)

/**
 * 获取公司列表
 */
const fetchCompanies = async (query: string = '') => {
  companyLoading.value = true
  try {
    const params = {
      pageNo: 1,
      pageSize: 20,
      buyName: query // 根据buyName搜索
    }
    const res = await ContractBuyApi.getContractBuyPage(params)
    companyOptions.value = res.list || []
  } catch (err) {
    message.error('获取公司列表失败')
  } finally {
    companyLoading.value = false
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ContractPayLogApi.getContractPayLogPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ContractPayLogApi.deleteContractPayLog(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ContractPayLogApi.exportContractPayLog(queryParams)
    download.excel(data, '合同缴费记录.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

// 定义打印机类型，确保包含所有必要字段
interface PrinterItem {
  contractPrinterId: number
  printeNo: string
  printerName: string
  totalColor?: number
  totalBlackWrtie?: number
  printerCountId?: number
  blackWhiteNumber: number
  colorNumber: number
}

// 记张相关变量
const recordDialogVisible = ref(false)
const recordFormRef = ref()
const recordLoading = ref(false)
const printerList = ref<PrinterItem[]>([])
const recordFormData = ref({
  payLogId: undefined,
  contractId: undefined,
  isPay: false // 添加缴费状态字段
})

/** 打开记张表单 */
const openRecordForm = async (row: ContractPayLogVO) => {
  recordFormData.value = {
    payLogId: row.payLogId,
    contractId: row.contractId,
    isPay: row.isPay // 传入缴费状态
  }

  try {
    const res = await ContractPrinterApi.getPrinterListByPayLogId(row.payLogId)
    printerList.value = res.map(printer => ({
      ...printer,
      blackWhiteNumber: Math.max(0, printer.totalBlackWrtie || 0),
      colorNumber: Math.max(0, printer.totalColor || 0)
    }))
    recordDialogVisible.value = true
  } catch (error) {
    message.error('获取打印机列表失败')
  }
}

/** 处理数字输入变化，确保不小于0 */
const handleNumberChange = (printer: PrinterItem, field: 'blackWhiteNumber' | 'colorNumber') => {
  if (printer[field] < 0) {
    printer[field] = 0
  }
  // 确保是整数
  if (printer[field] !== null && printer[field] !== undefined) {
    printer[field] = Math.floor(Math.abs(printer[field]))
  }
}

/** 提交记张数据 */
const submitRecord = async () => {
  try {
    recordLoading.value = true

    // 构建批量新增数据
    const batchData = []
    for (const printer of printerList.value) {
      // 移除张数大于0的判断，即使都为0也调用接口
      batchData.push({
        printerCountId: printer.printerCountId || undefined, // 传入printerCountId字段
        contractId: recordFormData.value.contractId,
        contractPrinterId: printer.contractPrinterId,
        payLogId: recordFormData.value.payLogId,
        totalColor: printer.colorNumber || 0, // 默认传0
        totalBlackWrtie: printer.blackWhiteNumber || 0 // 默认传0
      })
    }

    // 批量新增打印机计数记录
    if (batchData.length > 0) {
      await ContractPrinterCountApi.createContractPrinterCountBatch(batchData)
    }

    message.success('计数成功')
    recordDialogVisible.value = false
    await getList()
  } catch (error) {
    message.error('计数失败')
  } finally {
    recordLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
