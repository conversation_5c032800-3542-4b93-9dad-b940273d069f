/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    '.setting': typeof import('./../../.idea/.cache/.easy-yapi/.setting.size')['default']
    Account: typeof import('./../api/erp/finance/account/index.ts')['default']
    AccountForm: typeof import('./../views/erp/finance/account/AccountForm.vue')['default']
    Activity: typeof import('./../api/bpm/activity/index.ts')['default']
    AddNode: typeof import('./../components/SimpleProcessDesigner/src/addNode.vue')['default']
    Address: typeof import('./../api/member/address/index.ts')['default']
    AfterSale: typeof import('./../api/mall/trade/afterSale/index.ts')['default']
    AfterSaleDisagreeForm: typeof import('./../views/mall/trade/afterSale/form/AfterSaleDisagreeForm.vue')['default']
    AppLinkInput: typeof import('./../components/AppLinkInput/index.vue')['default']
    AppLinkSelectDialog: typeof import('./../components/AppLinkInput/AppLinkSelectDialog.vue')['default']
    Article: typeof import('./../api/mall/promotion/article/index.ts')['default']
    ArticleCategory: typeof import('./../api/mall/promotion/articleCategory/index.ts')['default']
    ArticleCategoryForm: typeof import('./../views/mall/promotion/article/category/ArticleCategoryForm.vue')['default']
    ArticleForm: typeof import('./../views/mall/promotion/article/ArticleForm.vue')['default']
    Backtop: typeof import('./../components/Backtop/src/Backtop.vue')['default']
    BalanceList: typeof import('./../views/member/user/components/balance-list.vue')['default']
    Banner: typeof import('./../api/mall/market/banner/index.ts')['default']
    BannerForm: typeof import('./../views/mall/promotion/banner/BannerForm.vue')['default']
    BargainActivity: typeof import('./../api/mall/promotion/bargain/bargainActivity.ts')['default']
    'BargainActivity.data': typeof import('./../views/mall/promotion/bargain/activity/bargainActivity.data.ts')['default']
    BargainActivityForm: typeof import('./../views/mall/promotion/bargain/activity/BargainActivityForm.vue')['default']
    BargainHelp: typeof import('./../api/mall/promotion/bargain/bargainHelp.ts')['default']
    BargainRecord: typeof import('./../api/mall/promotion/bargain/bargainRecord.ts')['default']
    BargainRecordListDialog: typeof import('./../views/mall/promotion/bargain/record/BargainRecordListDialog.vue')['default']
    Brand: typeof import('./../api/mall/product/brand.ts')['default']
    BrandForm: typeof import('./../views/mall/product/brand/BrandForm.vue')['default']
    BrokerageOrderListDialog: typeof import('./../views/mall/trade/brokerage/user/BrokerageOrderListDialog.vue')['default']
    BrokerageUserListDialog: typeof import('./../views/mall/trade/brokerage/user/BrokerageUserListDialog.vue')['default']
    BrokerageWithdrawRejectForm: typeof import('./../views/mall/trade/brokerage/withdraw/BrokerageWithdrawRejectForm.vue')['default']
    CardTitle: typeof import('./../components/Card/src/CardTitle.vue')['default']
    Category: typeof import('./../api/bpm/category/index.ts')['default']
    CategoryForm: typeof import('./../views/bpm/category/CategoryForm.vue')['default']
    Chat: typeof import('./../views/ai/chat/index.vue')['default']
    Check: typeof import('./../api/erp/stock/check/index.ts')['default']
    ColorInput: typeof import('./../components/ColorInput/index.vue')['default']
    CombinationActivity: typeof import('./../api/mall/promotion/combination/combinationActivity.ts')['default']
    'CombinationActivity.data': typeof import('./../views/mall/promotion/combination/activity/combinationActivity.data.ts')['default']
    CombinationActivityForm: typeof import('./../views/mall/promotion/combination/activity/CombinationActivityForm.vue')['default']
    CombinationRecord: typeof import('./../api/mall/promotion/combination/combinationRecord.ts')['default']
    CombinationRecordListDialog: typeof import('./../views/mall/promotion/combination/record/CombinationRecordListDialog.vue')['default']
    Comment: typeof import('./../api/mall/product/comment.ts')['default']
    CommentForm: typeof import('./../views/mall/product/comment/CommentForm.vue')['default']
    Common: typeof import('./../api/mall/statistics/common.ts')['default']
    ComparisonCard: typeof import('./../views/mall/home/<USER>/ComparisonCard.vue')['default']
    ComponentContainer: typeof import('./../components/DiyEditor/components/ComponentContainer.vue')['default']
    ComponentContainerProperty: typeof import('./../components/DiyEditor/components/ComponentContainerProperty.vue')['default']
    ComponentLibrary: typeof import('./../components/DiyEditor/components/ComponentLibrary.vue')['default']
    Components: typeof import('./../views/mall/promotion/components/index.ts')['default']
    Config: typeof import('./../api/member/config/index.ts')['default']
    ConfigGlobal: typeof import('./../components/ConfigGlobal/src/ConfigGlobal.vue')['default']
    ContentDetailWrap: typeof import('./../components/ContentDetailWrap/src/ContentDetailWrap.vue')['default']
    ContentWrap: typeof import('./../components/ContentWrap/src/ContentWrap.vue')['default']
    Contractbuy: typeof import('./../api/hzjc/contractbuy/index.ts')['default']
    Contractpaylog: typeof import('./../api/hzjc/contractpaylog/index.ts')['default']
    ContractPayLogForm: typeof import('./../views/bussiness/contractpaylog/ContractPayLogForm.vue')['default']
    Contractprinter: typeof import('./../api/hzjc/contractprinter/index.ts')['default']
    ContractPrinterController: typeof import('./../../cn/hz/jc/module/hzjc/controller/admin/contractprinter/ContractPrinterController.java')['default']
    Contractprintercount: typeof import('./../api/hzjc/contractprintercount/index.ts')['default']
    CopperModal: typeof import('./../components/Cropper/src/CopperModal.vue')['default']
    Copy: typeof import('./../views/bpm/task/copy/index.vue')['default']
    CountTo: typeof import('./../components/CountTo/src/CountTo.vue')['default']
    Coupon: typeof import('./../api/mall/promotion/coupon/coupon.ts')['default']
    CouponSelect: typeof import('./../views/mall/promotion/coupon/components/CouponSelect.vue')['default']
    CouponSendForm: typeof import('./../views/mall/promotion/coupon/components/CouponSendForm.vue')['default']
    CouponTemplate: typeof import('./../api/mall/promotion/coupon/couponTemplate.ts')['default']
    CouponTemplateForm: typeof import('./../views/mall/promotion/coupon/template/CouponTemplateForm.vue')['default']
    Create: typeof import('./../views/bpm/processInstance/create/index.vue')['default']
    Crontab: typeof import('./../components/Crontab/src/Crontab.vue')['default']
    Cropper: typeof import('./../components/Cropper/src/Cropper.vue')['default']
    CropperAvatar: typeof import('./../components/Cropper/src/CropperAvatar.vue')['default']
    Customer: typeof import('./../api/erp/sale/customer/index.ts')['default']
    CustomerForm: typeof import('./../views/erp/sale/customer/CustomerForm.vue')['default']
    Decorate: typeof import('./../views/mall/promotion/diy/page/decorate.vue')['default']
    Definition: typeof import('./../api/bpm/definition/index.ts')['default']
    DeliveryForm: typeof import('./../views/mall/product/spu/form/DeliveryForm.vue')['default']
    DescriptionForm: typeof import('./../views/mall/product/spu/form/DescriptionForm.vue')['default']
    Descriptions: typeof import('./../components/Descriptions/src/Descriptions.vue')['default']
    DescriptionsItemLabel: typeof import('./../components/Descriptions/src/DescriptionsItemLabel.vue')['default']
    Detail: typeof import('./../views/bpm/oa/leave/detail.vue')['default']
    Dialog: typeof import('./../components/Dialog/src/Dialog.vue')['default']
    DictSelect: typeof import('./../components/FormCreate/src/components/DictSelect.vue')['default']
    DictTag: typeof import('./../components/DictTag/src/DictTag.vue')['default']
    DiscountActivity: typeof import('./../api/mall/promotion/discount/discountActivity.ts')['default']
    'DiscountActivity.data': typeof import('./../views/mall/promotion/discountActivity/discountActivity.data.ts')['default']
    DiscountActivityForm: typeof import('./../views/mall/promotion/discountActivity/DiscountActivityForm.vue')['default']
    DiyEditor: typeof import('./../components/DiyEditor/index.vue')['default']
    DiyPageForm: typeof import('./../views/mall/promotion/diy/page/DiyPageForm.vue')['default']
    DiyTemplateForm: typeof import('./../views/mall/promotion/diy/template/DiyTemplateForm.vue')['default']
    DocAlert: typeof import('./../components/DocAlert/index.vue')['default']
    Done: typeof import('./../views/bpm/task/done/index.vue')['default']
    Draggable: typeof import('./../components/Draggable/index.vue')['default']
    Echart: typeof import('./../components/Echart/src/Echart.vue')['default']
    Editor: typeof import('./../components/Editor/src/Editor.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElementBaseInfo: typeof import('./../components/bpmnProcessDesigner/package/penal/base/ElementBaseInfo.vue')['default']
    ElementForm: typeof import('./../components/bpmnProcessDesigner/package/penal/form/ElementForm.vue')['default']
    ElementListeners: typeof import('./../components/bpmnProcessDesigner/package/penal/listeners/ElementListeners.vue')['default']
    ElementMultiInstance: typeof import('./../components/bpmnProcessDesigner/package/penal/multi-instance/ElementMultiInstance.vue')['default']
    ElementOtherConfig: typeof import('./../components/bpmnProcessDesigner/package/penal/other/ElementOtherConfig.vue')['default']
    ElementProperties: typeof import('./../components/bpmnProcessDesigner/package/penal/properties/ElementProperties.vue')['default']
    ElementTask: typeof import('./../components/bpmnProcessDesigner/package/penal/task/ElementTask.vue')['default']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImageViewer: typeof import('element-plus/es')['ElImageViewer']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    Error: typeof import('./../components/Error/src/Error.vue')['default']
    ExperienceRecord: typeof import('./../api/member/experience-record/index.ts')['default']
    Express: typeof import('./../api/mall/trade/delivery/express/index.ts')['default']
    ExpressForm: typeof import('./../views/mall/trade/delivery/express/ExpressForm.vue')['default']
    ExpressTemplate: typeof import('./../api/mall/trade/delivery/expressTemplate/index.ts')['default']
    ExpressTemplateForm: typeof import('./../views/mall/trade/delivery/expressTemplate/ExpressTemplateForm.vue')['default']
    Favorite: typeof import('./../api/mall/product/favorite.ts')['default']
    FinancePaymentForm: typeof import('./../views/erp/finance/payment/FinancePaymentForm.vue')['default']
    FinancePaymentItemForm: typeof import('./../views/erp/finance/payment/components/FinancePaymentItemForm.vue')['default']
    FinanceReceiptForm: typeof import('./../views/erp/finance/receipt/FinanceReceiptForm.vue')['default']
    FinanceReceiptItemForm: typeof import('./../views/erp/finance/receipt/components/FinanceReceiptItemForm.vue')['default']
    FlowCondition: typeof import('./../components/bpmnProcessDesigner/package/penal/flow-condition/FlowCondition.vue')['default']
    Form: typeof import('./../components/Form/src/Form.vue')['default']
    Formatter: typeof import('./../views/mall/promotion/coupon/formatter.ts')['default']
    Goview: typeof import('./../views/report/goview/index.vue')['default']
    Group: typeof import('./../api/member/group/index.ts')['default']
    GroupForm: typeof import('./../views/member/group/GroupForm.vue')['default']
    Highlight: typeof import('./../components/Highlight/src/Highlight.vue')['default']
    Home: typeof import('./../views/erp/home/<USER>')['default']
    Icon: typeof import('./../components/Icon/src/Icon.vue')['default']
    IconSelect: typeof import('./../components/Icon/src/IconSelect.vue')['default']
    IFrame: typeof import('./../components/IFrame/src/IFrame.vue')['default']
    ImageViewer: typeof import('./../components/ImageViewer/src/ImageViewer.vue')['default']
    In: typeof import('./../api/erp/purchase/in/index.ts')['default']
    InfoForm: typeof import('./../views/mall/product/spu/form/InfoForm.vue')['default']
    Infotip: typeof import('./../components/Infotip/src/Infotip.vue')['default']
    InputPassword: typeof import('./../components/InputPassword/src/InputPassword.vue')['default']
    InputWithColor: typeof import('./../components/InputWithColor/index.vue')['default']
    Jmreport: typeof import('./../views/report/jmreport/index.vue')['default']
    Leave: typeof import('./../api/bpm/leave/index.ts')['default']
    Level: typeof import('./../api/member/level/index.ts')['default']
    LevelForm: typeof import('./../views/member/level/LevelForm.vue')['default']
    MagicCubeEditor: typeof import('./../components/MagicCubeEditor/index.vue')['default']
    Manager: typeof import('./../views/bpm/processInstance/manager/index.vue')['default']
    Member: typeof import('./../api/mall/statistics/member.ts')['default']
    MemberFunnelCard: typeof import('./../views/mall/statistics/member/components/MemberFunnelCard.vue')['default']
    MemberGroupSelect: typeof import('./../views/member/group/components/MemberGroupSelect.vue')['default']
    MemberLevelSelect: typeof import('./../views/member/level/components/MemberLevelSelect.vue')['default']
    MemberStatisticsCard: typeof import('./../views/mall/home/<USER>/MemberStatisticsCard.vue')['default']
    MemberTagSelect: typeof import('./../views/member/tag/components/MemberTagSelect.vue')['default']
    MemberTerminalCard: typeof import('./../views/mall/statistics/member/components/MemberTerminalCard.vue')['default']
    Model: typeof import('./../api/bpm/model/index.ts')['default']
    ModelForm: typeof import('./../views/bpm/model/ModelForm.vue')['default']
    ModelImportForm: typeof import('./../views/bpm/model/ModelImportForm.vue')['default']
    Move: typeof import('./../api/erp/stock/move/index.ts')['default']
    NodeWrap: typeof import('./../components/SimpleProcessDesigner/src/nodeWrap.vue')['default']
    OperateLogV2: typeof import('./../components/OperateLogV2/src/OperateLogV2.vue')['default']
    OperationDataCard: typeof import('./../views/mall/home/<USER>/OperationDataCard.vue')['default']
    Order: typeof import('./../api/erp/sale/order/index.ts')['default']
    OrderDeliveryForm: typeof import('./../views/mall/trade/order/form/OrderDeliveryForm.vue')['default']
    OrderPickUpForm: typeof import('./../views/mall/trade/order/form/OrderPickUpForm.vue')['default']
    OrderTableColumn: typeof import('./../views/mall/trade/order/components/OrderTableColumn.vue')['default']
    OrderUpdateAddressForm: typeof import('./../views/mall/trade/order/form/OrderUpdateAddressForm.vue')['default']
    OrderUpdatePriceForm: typeof import('./../views/mall/trade/order/form/OrderUpdatePriceForm.vue')['default']
    OrderUpdateRemarkForm: typeof import('./../views/mall/trade/order/form/OrderUpdateRemarkForm.vue')['default']
    OtherForm: typeof import('./../views/mall/product/spu/form/OtherForm.vue')['default']
    Out: typeof import('./../api/erp/sale/out/index.ts')['default']
    Page: typeof import('./../api/mall/promotion/diy/page.ts')['default']
    Pagination: typeof import('./../components/Pagination/index.vue')['default']
    Pay: typeof import('./../api/mall/statistics/pay.ts')['default']
    Payment: typeof import('./../api/erp/finance/payment/index.ts')['default']
    PaymentDialogOptimization: typeof import('./../../docs/payment-dialog-optimization.md')['default']
    PickUpOrder: typeof import('./../views/mall/trade/delivery/pickUpOrder/index.vue')['default']
    PickUpStore: typeof import('./../api/mall/trade/delivery/pickUpStore/index.ts')['default']
    PickUpStoreForm: typeof import('./../views/mall/trade/delivery/pickUpStore/PickUpStoreForm.vue')['default']
    ProcessDesigner: typeof import('./../components/bpmnProcessDesigner/package/designer/ProcessDesigner.vue')['default']
    ProcessExpression: typeof import('./../api/bpm/processExpression/index.ts')['default']
    ProcessExpressionDialog: typeof import('./../components/bpmnProcessDesigner/package/penal/task/task-components/ProcessExpressionDialog.vue')['default']
    ProcessExpressionForm: typeof import('./../views/bpm/processExpression/ProcessExpressionForm.vue')['default']
    ProcessInstance: typeof import('./../api/bpm/processInstance/index.ts')['default']
    ProcessInstanceBpmnViewer: typeof import('./../views/bpm/processInstance/detail/ProcessInstanceBpmnViewer.vue')['default']
    ProcessInstanceTaskList: typeof import('./../views/bpm/processInstance/detail/ProcessInstanceTaskList.vue')['default']
    ProcessListener: typeof import('./../api/bpm/processListener/index.ts')['default']
    ProcessListenerDialog: typeof import('./../components/bpmnProcessDesigner/package/penal/listeners/ProcessListenerDialog.vue')['default']
    ProcessListenerForm: typeof import('./../views/bpm/processListener/ProcessListenerForm.vue')['default']
    ProcessPalette: typeof import('./../components/bpmnProcessDesigner/package/palette/ProcessPalette.vue')['default']
    ProcessViewer: typeof import('./../components/bpmnProcessDesigner/package/designer/ProcessViewer.vue')['default']
    Product: typeof import('./../api/erp/product/product/index.ts')['default']
    ProductAttributes: typeof import('./../views/mall/product/spu/form/ProductAttributes.vue')['default']
    ProductCategoryForm: typeof import('./../views/erp/product/category/ProductCategoryForm.vue')['default']
    ProductCategorySelect: typeof import('./../views/mall/product/category/components/ProductCategorySelect.vue')['default']
    ProductForm: typeof import('./../views/erp/product/product/ProductForm.vue')['default']
    ProductPropertyAddForm: typeof import('./../views/mall/product/spu/form/ProductPropertyAddForm.vue')['default']
    ProductRank: typeof import('./../views/mall/statistics/product/components/ProductRank.vue')['default']
    ProductSummary: typeof import('./../views/mall/statistics/product/components/ProductSummary.vue')['default']
    ProductUnitForm: typeof import('./../views/erp/product/unit/ProductUnitForm.vue')['default']
    PropertiesPanel: typeof import('./../components/bpmnProcessDesigner/package/penal/PropertiesPanel.vue')['default']
    Property: typeof import('./../api/mall/product/property.ts')['default']
    PropertyForm: typeof import('./../views/mall/product/property/PropertyForm.vue')['default']
    Purchase: typeof import('./../api/erp/statistics/purchase/index.ts')['default']
    PurchaseInForm: typeof import('./../views/erp/purchase/in/PurchaseInForm.vue')['default']
    PurchaseInItemForm: typeof import('./../views/erp/purchase/in/components/PurchaseInItemForm.vue')['default']
    PurchaseInPaymentEnableList: typeof import('./../views/erp/purchase/in/components/PurchaseInPaymentEnableList.vue')['default']
    PurchaseOrderForm: typeof import('./../views/erp/purchase/order/PurchaseOrderForm.vue')['default']
    PurchaseOrderInEnableList: typeof import('./../views/erp/purchase/order/components/PurchaseOrderInEnableList.vue')['default']
    PurchaseOrderItemForm: typeof import('./../views/erp/purchase/order/components/PurchaseOrderItemForm.vue')['default']
    PurchaseOrderReturnEnableList: typeof import('./../views/erp/purchase/order/components/PurchaseOrderReturnEnableList.vue')['default']
    PurchaseReturnForm: typeof import('./../views/erp/purchase/return/PurchaseReturnForm.vue')['default']
    PurchaseReturnItemForm: typeof import('./../views/erp/purchase/return/components/PurchaseReturnItemForm.vue')['default']
    PurchaseReturnRefundEnableList: typeof import('./../views/erp/purchase/return/components/PurchaseReturnRefundEnableList.vue')['default']
    Qrcode: typeof import('./../components/Qrcode/src/Qrcode.vue')['default']
    Receipt: typeof import('./../api/erp/finance/receipt/index.ts')['default']
    ReceiveTask: typeof import('./../components/bpmnProcessDesigner/package/penal/task/task-components/ReceiveTask.vue')['default']
    Record: typeof import('./../api/erp/stock/record/index.ts')['default']
    ReplyForm: typeof import('./../views/mall/product/comment/ReplyForm.vue')['default']
    Return: typeof import('./../api/erp/sale/return/index.ts')['default']
    RewardActivity: typeof import('./../api/mall/promotion/reward/rewardActivity.ts')['default']
    RewardForm: typeof import('./../views/mall/promotion/rewardActivity/RewardForm.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterSearch: typeof import('./../components/RouterSearch/index.vue')['default']
    RouterView: typeof import('vue-router')['RouterView']
    Sale: typeof import('./../api/erp/statistics/sale/index.ts')['default']
    SaleOrderForm: typeof import('./../views/erp/sale/order/SaleOrderForm.vue')['default']
    SaleOrderItemForm: typeof import('./../views/erp/sale/order/components/SaleOrderItemForm.vue')['default']
    SaleOrderOutEnableList: typeof import('./../views/erp/sale/order/components/SaleOrderOutEnableList.vue')['default']
    SaleOrderReturnEnableList: typeof import('./../views/erp/sale/order/components/SaleOrderReturnEnableList.vue')['default']
    SaleOutForm: typeof import('./../views/erp/sale/out/SaleOutForm.vue')['default']
    SaleOutItemForm: typeof import('./../views/erp/sale/out/components/SaleOutItemForm.vue')['default']
    SaleOutReceiptEnableList: typeof import('./../views/erp/sale/out/components/SaleOutReceiptEnableList.vue')['default']
    SaleReturnForm: typeof import('./../views/erp/sale/return/SaleReturnForm.vue')['default']
    SaleReturnItemForm: typeof import('./../views/erp/sale/return/components/SaleReturnItemForm.vue')['default']
    SaleReturnRefundEnableList: typeof import('./../views/erp/sale/return/components/SaleReturnRefundEnableList.vue')['default']
    ScriptTask: typeof import('./../components/bpmnProcessDesigner/package/penal/task/task-components/ScriptTask.vue')['default']
    Search: typeof import('./../components/Search/src/Search.vue')['default']
    SeckillActivity: typeof import('./../api/mall/promotion/seckill/seckillActivity.ts')['default']
    'SeckillActivity.data': typeof import('./../views/mall/promotion/seckill/activity/seckillActivity.data.ts')['default']
    SeckillActivityForm: typeof import('./../views/mall/promotion/seckill/activity/SeckillActivityForm.vue')['default']
    SeckillConfig: typeof import('./../api/mall/promotion/seckill/seckillConfig.ts')['default']
    SeckillConfigForm: typeof import('./../views/mall/promotion/seckill/config/SeckillConfigForm.vue')['default']
    ShortcutCard: typeof import('./../views/mall/home/<USER>/ShortcutCard.vue')['default']
    ShortcutDateRangePicker: typeof import('./../components/ShortcutDateRangePicker/index.vue')['default']
    SignalAndMessage: typeof import('./../components/bpmnProcessDesigner/package/penal/signal-message/SignalAndMessage.vue')['default']
    SignInConfigForm: typeof import('./../views/member/signin/config/SignInConfigForm.vue')['default']
    SimpleWorkflow: typeof import('./../views/bpm/simpleWorkflow/index.vue')['default']
    SkuForm: typeof import('./../views/mall/product/spu/form/SkuForm.vue')['default']
    SkuList: typeof import('./../views/mall/product/spu/components/SkuList.vue')['default']
    SkuTableSelect: typeof import('./../views/mall/product/spu/components/SkuTableSelect.vue')['default']
    Spu: typeof import('./../api/mall/product/spu.ts')['default']
    SpuAndSkuList: typeof import('./../views/mall/promotion/components/SpuAndSkuList.vue')['default']
    SpuSelect: typeof import('./../views/mall/promotion/components/SpuSelect.vue')['default']
    SpuShowcase: typeof import('./../views/mall/product/spu/components/SpuShowcase.vue')['default']
    SpuTableSelect: typeof import('./../views/mall/product/spu/components/SpuTableSelect.vue')['default']
    Sticky: typeof import('./../components/Sticky/src/Sticky.vue')['default']
    Stock: typeof import('./../api/erp/stock/stock/index.ts')['default']
    StockCheckForm: typeof import('./../views/erp/stock/check/StockCheckForm.vue')['default']
    StockCheckItemForm: typeof import('./../views/erp/stock/check/components/StockCheckItemForm.vue')['default']
    StockInForm: typeof import('./../views/erp/stock/in/StockInForm.vue')['default']
    StockInItemForm: typeof import('./../views/erp/stock/in/components/StockInItemForm.vue')['default']
    StockMoveForm: typeof import('./../views/erp/stock/move/StockMoveForm.vue')['default']
    StockMoveItemForm: typeof import('./../views/erp/stock/move/components/StockMoveItemForm.vue')['default']
    StockOutForm: typeof import('./../views/erp/stock/out/StockOutForm.vue')['default']
    StockOutItemForm: typeof import('./../views/erp/stock/out/components/StockOutItemForm.vue')['default']
    SummaryCard: typeof import('./../components/SummaryCard/index.vue')['default']
    Supplier: typeof import('./../api/erp/purchase/supplier/index.ts')['default']
    SupplierForm: typeof import('./../views/erp/purchase/supplier/SupplierForm.vue')['default']
    SystemNameChanges: typeof import('./../../docs/system-name-changes.md')['default']
    Table: typeof import('./../components/Table/src/Table.vue')['default']
    TableLayoutOptimization: typeof import('./../../docs/table-layout-optimization.md')['default']
    TableSelectForm: typeof import('./../components/Table/src/TableSelectForm.vue')['default']
    Tag: typeof import('./../api/member/tag/index.ts')['default']
    TagForm: typeof import('./../views/member/tag/TagForm.vue')['default']
    Task: typeof import('./../api/bpm/task/index.ts')['default']
    TaskDelegateForm: typeof import('./../views/bpm/processInstance/detail/dialog/TaskDelegateForm.vue')['default']
    TaskReturnForm: typeof import('./../views/bpm/processInstance/detail/dialog/TaskReturnForm.vue')['default']
    TaskSignCreateForm: typeof import('./../views/bpm/processInstance/detail/dialog/TaskSignCreateForm.vue')['default']
    TaskSignDeleteForm: typeof import('./../views/bpm/processInstance/detail/dialog/TaskSignDeleteForm.vue')['default']
    TaskSignList: typeof import('./../views/bpm/processInstance/detail/dialog/TaskSignList.vue')['default']
    TaskTransferForm: typeof import('./../views/bpm/processInstance/detail/dialog/TaskTransferForm.vue')['default']
    Template: typeof import('./../api/mall/promotion/diy/template.ts')['default']
    TimeSummaryChart: typeof import('./../views/erp/home/<USER>/TimeSummaryChart.vue')['default']
    Todo: typeof import('./../views/bpm/task/todo/index.vue')['default']
    Tooltip: typeof import('./../components/Tooltip/src/Tooltip.vue')['default']
    Trade: typeof import('./../api/mall/statistics/trade.ts')['default']
    TradeStatisticValue: typeof import('./../views/mall/statistics/trade/components/TradeStatisticValue.vue')['default']
    TradeTrendCard: typeof import('./../views/mall/home/<USER>/TradeTrendCard.vue')['default']
    Unit: typeof import('./../api/erp/product/unit/index.ts')['default']
    UpdateBindUserForm: typeof import('./../views/mall/trade/brokerage/user/UpdateBindUserForm.vue')['default']
    UploadFile: typeof import('./../components/UploadFile/src/UploadFile.vue')['default']
    UploadImg: typeof import('./../components/UploadFile/src/UploadImg.vue')['default']
    UploadImgs: typeof import('./../components/UploadFile/src/UploadImgs.vue')['default']
    User: typeof import('./../api/member/user/index.ts')['default']
    UserAccountInfo: typeof import('./../views/member/user/detail/UserAccountInfo.vue')['default']
    UserAddressList: typeof import('./../views/member/user/detail/UserAddressList.vue')['default']
    UserBasicInfo: typeof import('./../views/member/user/detail/UserBasicInfo.vue')['default']
    UserBrokerageList: typeof import('./../views/member/user/detail/UserBrokerageList.vue')['default']
    UserCouponList: typeof import('./../views/member/user/detail/UserCouponList.vue')['default']
    UserExperienceRecordList: typeof import('./../views/member/user/detail/UserExperienceRecordList.vue')['default']
    UserFavoriteList: typeof import('./../views/member/user/detail/UserFavoriteList.vue')['default']
    UserForm: typeof import('./../views/member/user/UserForm.vue')['default']
    UserGroup: typeof import('./../api/bpm/userGroup/index.ts')['default']
    UserGroupForm: typeof import('./../views/bpm/group/UserGroupForm.vue')['default']
    UserLevelUpdateForm: typeof import('./../views/member/user/UserLevelUpdateForm.vue')['default']
    UserOrderList: typeof import('./../views/member/user/detail/UserOrderList.vue')['default']
    UserPointList: typeof import('./../views/member/user/detail/UserPointList.vue')['default']
    UserPointUpdateForm: typeof import('./../views/member/user/UserPointUpdateForm.vue')['default']
    UserSignList: typeof import('./../views/member/user/detail/UserSignList.vue')['default']
    UserTask: typeof import('./../components/bpmnProcessDesigner/package/penal/task/task-components/UserTask.vue')['default']
    UserTaskListeners: typeof import('./../components/bpmnProcessDesigner/package/penal/listeners/UserTaskListeners.vue')['default']
    Value: typeof import('./../views/mall/product/property/value/index.vue')['default']
    ValueForm: typeof import('./../views/mall/product/property/value/ValueForm.vue')['default']
    Verify: typeof import('./../components/Verifition/src/Verify.vue')['default']
    VerifyPoints: typeof import('./../components/Verifition/src/Verify/VerifyPoints.vue')['default']
    VerifySlide: typeof import('./../components/Verifition/src/Verify/VerifySlide.vue')['default']
    VerticalButtonGroup: typeof import('./../components/VerticalButtonGroup/index.vue')['default']
    Warehouse: typeof import('./../api/erp/stock/warehouse/index.ts')['default']
    WarehouseForm: typeof import('./../views/erp/stock/warehouse/WarehouseForm.vue')['default']
    Withdraw: typeof import('./../api/mall/trade/brokerage/withdraw/index.ts')['default']
    XButton: typeof import('./../components/XButton/src/XButton.vue')['default']
    XTextButton: typeof import('./../components/XButton/src/XTextButton.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
