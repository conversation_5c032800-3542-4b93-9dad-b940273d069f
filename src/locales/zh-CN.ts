export default {
  common: {
    inputText: '请输入',
    selectText: '请选择',
    startTimeText: '开始时间',
    endTimeText: '结束时间',
    login: '登录',
    required: '该项为必填项',
    loginOut: '退出系统',
    document: '项目文档',
    profile: '个人中心',
    reminder: '温馨提示',
    loginOutMessage: '是否退出本系统？',
    back: '返回',
    ok: '确定',
    save: '保存',
    cancel: '取消',
    close: '关闭',
    reload: '重新加载',
    success: '成功',
    closeTab: '关闭标签页',
    closeTheLeftTab: '关闭左侧标签页',
    closeTheRightTab: '关闭右侧标签页',
    closeOther: '关闭其他标签页',
    closeAll: '关闭全部标签页',
    prevLabel: '上一步',
    nextLabel: '下一步',
    skipLabel: '跳过',
    doneLabel: '结束',
    menu: '菜单',
    menuDes: '以路由的结构渲染的菜单栏',
    collapse: '展开缩收',
    collapseDes: '展开和缩放菜单栏',
    tagsView: '标签页',
    tagsViewDes: '用于记录路由历史记录',
    tool: '工具',
    toolDes: '用于设置定制系统',
    query: '查询',
    reset: '重置',
    shrink: '收起',
    expand: '展开',
    confirmTitle: '系统提示',
    exportMessage: '是否确认导出数据项？',
    importMessage: '是否确认导入数据项？',
    createSuccess: '新增成功',
    updateSuccess: '修改成功',
    delMessage: '是否删除所选中数据？',
    delDataMessage: '是否删除数据？',
    delNoData: '请选择需要删除的数据',
    delSuccess: '删除成功',
    index: '序号',
    status: '状态',
    createTime: '创建时间',
    updateTime: '更新时间',
    copy: '复制',
    copySuccess: '复制成功',
    copyError: '复制失败'
  },
  lock: {
    lockScreen: '锁定屏幕',
    lock: '锁定',
    lockPassword: '锁屏密码',
    unlock: '点击解锁',
    backToLogin: '返回登录',
    entrySystem: '进入系统',
    placeholder: '请输入锁屏密码',
    message: '锁屏密码错误'
  },
  error: {
    noPermission: `抱歉，您无权访问此页面。`,
    pageError: '抱歉，您访问的页面不存在。',
    networkError: '抱歉，服务器报告错误。',
    returnToHome: '返回首页'
  },
  permission: {
    hasPermission: `请设置操作权限标签值`,
    hasRole: `请设置角色权限标签值`
  },
  setting: {
    projectSetting: '项目配置',
    theme: '主题',
    layout: '布局',
    systemTheme: '系统主题',
    menuTheme: '菜单主题',
    interfaceDisplay: '界面显示',
    breadcrumb: '面包屑',
    breadcrumbIcon: '面包屑图标',
    collapseMenu: '折叠菜单',
    hamburgerIcon: '折叠图标',
    screenfullIcon: '全屏图标',
    sizeIcon: '尺寸图标',
    localeIcon: '多语言图标',
    messageIcon: '消息图标',
    tagsView: '标签页',
    logo: '标志',
    greyMode: '灰色模式',
    fixedHeader: '固定头部',
    headerTheme: '头部主题',
    cutMenu: '切割菜单',
    copy: '拷贝',
    clearAndReset: '清除缓存并且重置',
    copySuccess: '拷贝成功',
    copyFailed: '拷贝失败',
    footer: '页脚',
    uniqueOpened: '菜单手风琴',
    tagsViewIcon: '标签页图标',
    reExperienced: '请重新退出登录体验',
    fixedMenu: '固定菜单'
  },
  size: {
    default: '默认',
    large: '大',
    small: '小'
  },
  login: {
    welcome: '欢迎使用杭州嘉程管理系统',
    message: '专业的企业管理解决方案',
    tenantname: '租户名称',
    username: '用户名',
    password: '密码',
    code: '验证码',
    login: '登录',
    relogin: '重新登录',
    otherLogin: '其他登录方式',
    register: '注册',
    checkPassword: '确认密码',
    remember: '记住我',
    hasUser: '已有账号？去登录',
    forgetPassword: '忘记密码?',
    tenantNamePlaceholder: '请输入租户名称',
    usernamePlaceholder: '请输入用户名',
    passwordPlaceholder: '请输入密码',
    codePlaceholder: '请输入验证码',
    mobileTitle: '手机登录',
    mobileNumber: '手机号码',
    mobileNumberPlaceholder: '请输入手机号码',
    backLogin: '返回',
    getSmsCode: '获取验证码',
    btnMobile: '手机登录',
    btnQRCode: '二维码登录',
    qrcode: '扫描二维码登录',
    btnRegister: '注册',
    SmsSendMsg: '验证码已发送'
  },
  captcha: {
    verification: '请完成安全验证',
    slide: '向右滑动完成验证',
    point: '请依次点击',
    success: '验证成功',
    fail: '验证失败'
  },
  router: {
    login: '登录',
    socialLogin: '社交登录',
    home: '首页',
    analysis: '分析页',
    workplace: '工作台'
  },
  analysis: {
    newUser: '新增用户',
    unreadInformation: '未读消息',
    transactionAmount: '成交金额',
    totalShopping: '购物总量',
    monthlySales: '每月销售额',
    userAccessSource: '用户访问来源',
    january: '一月',
    february: '二月',
    march: '三月',
    april: '四月',
    may: '五月',
    june: '六月',
    july: '七月',
    august: '八月',
    september: '九月',
    october: '十月',
    november: '十一月',
    december: '十二月',
    estimate: '预计',
    actual: '实际',
    directAccess: '直接访问',
    mailMarketing: '邮件营销',
    allianceAdvertising: '联盟广告',
    videoAdvertising: '视频广告',
    searchEngines: '搜索引擎',
    weeklyUserActivity: '每周用户活跃量',
    activeQuantity: '活跃量',
    monday: '周一',
    tuesday: '周二',
    wednesday: '周三',
    thursday: '周四',
    friday: '周五',
    saturday: '周六',
    sunday: '周日'
  },
  workplace: {
    welcome: '你好',
    happyDay: '祝你开心每一天!',
    toady: '今日晴',
    notice: '通知公告',
    project: '项目数',
    access: '项目访问',
    toDo: '待办',
    introduction: '一个正经的简介',
    shortcutOperation: '快捷入口',
    operation: '操作',
    index: '指数',
    personal: '个人',
    team: '团队',
    quote: '引用',
    contribution: '贡献',
    hot: '热度',
    yield: '产量',
    dynamic: '动态',
    push: '推送',
    follow: '关注'
  },
  form: {
    input: '输入框',
    inputNumber: '数字输入框',
    default: '默认',
    icon: '图标',
    mixed: '复合型',
    textarea: '多行文本',
    slot: '插槽',
    position: '位置',
    autocomplete: '自动补全',
    select: '选择器',
    selectGroup: '选项分组',
    selectV2: '虚拟列表选择器',
    cascader: '级联选择器',
    switch: '开关',
    rate: '评分',
    colorPicker: '颜色选择器',
    transfer: '穿梭框',
    render: '渲染器',
    radio: '单选框',
    button: '按钮',
    checkbox: '多选框',
    slider: '滑块',
    datePicker: '日期选择器',
    shortcuts: '快捷选项',
    today: '今天',
    yesterday: '昨天',
    aWeekAgo: '一周前',
    week: '周',
    year: '年',
    month: '月',
    dates: '日期',
    daterange: '日期范围',
    monthrange: '月份范围',
    dateTimePicker: '日期时间选择器',
    dateTimerange: '日期时间范围',
    timePicker: '时间选择器',
    timeSelect: '时间选择',
    inputPassword: '密码输入框',
    passwordStrength: '密码强度',
    operate: '操作',
    change: '更改',
    restore: '还原',
    disabled: '禁用',
    disablement: '解除禁用',
    delete: '删除',
    add: '添加',
    setValue: '设置值',
    resetValue: '重置值',
    set: '设置',
    subitem: '子项',
    formValidation: '表单验证',
    verifyReset: '验证重置',
    remark: '备注'
  },
  watermark: {
    watermark: '水印'
  },
  table: {
    table: '表格',
    index: '序号',
    title: '标题',
    author: '作者',
    createTime: '创建时间',
    action: '操作',
    pagination: '分页',
    reserveIndex: '叠加序号',
    restoreIndex: '还原序号',
    showSelections: '显示多选',
    hiddenSelections: '隐藏多选',
    showExpandedRows: '显示展开行',
    hiddenExpandedRows: '隐藏展开行',
    header: '头部'
  },
  action: {
    create: '新增',
    add: '新增',
    del: '删除',
    delete: '删除',
    edit: '编辑',
    update: '编辑',
    preview: '预览',
    more: '更多',
    sync: '同步',
    save: '保存',
    detail: '详情',
    export: '导出',
    import: '导入',
    generate: '生成',
    logout: '强制退出',
    test: '测试',
    typeCreate: '字典类型新增',
    typeUpdate: '字典类型编辑',
    dataCreate: '字典数据新增',
    dataUpdate: '字典数据编辑'
  },
  dialog: {
    dialog: '弹窗',
    open: '打开',
    close: '关闭'
  },
  sys: {
    api: {
      operationFailed: '操作失败',
      errorTip: '错误提示',
      errorMessage: '操作失败,系统异常!',
      timeoutMessage: '登录超时,请重新登录!',
      apiTimeoutMessage: '接口请求超时,请刷新页面重试!',
      apiRequestFailed: '请求出错，请稍候重试',
      networkException: '网络异常',
      networkExceptionMsg: '网络异常，请检查您的网络连接是否正常!',
      errMsg401: '用户没有权限（令牌、用户名、密码错误）!',
      errMsg403: '用户得到授权，但是访问是被禁止的。!',
      errMsg404: '网络请求错误,未找到该资源!',
      errMsg405: '网络请求错误,请求方法未允许!',
      errMsg408: '网络请求超时!',
      errMsg500: '服务器错误,请联系管理员!',
      errMsg501: '网络未实现!',
      errMsg502: '网络错误!',
      errMsg503: '服务不可用，服务器暂时过载或维护!',
      errMsg504: '网络超时!',
      errMsg505: 'http版本不支持该请求!',
      errMsg901: '演示模式，无法进行写操作!'
    },
    app: {
      logoutTip: '温馨提醒',
      logoutMessage: '是否确认退出系统?',
      menuLoading: '菜单加载中...'
    },
    exception: {
      backLogin: '返回登录',
      backHome: '返回首页',
      subTitle403: '抱歉，您无权访问此页面。',
      subTitle404: '抱歉，您访问的页面不存在。',
      subTitle500: '抱歉，服务器报告错误。',
      noDataTitle: '当前页无数据',
      networkErrorTitle: '网络错误',
      networkErrorSubTitle: '抱歉，您的网络连接已断开，请检查您的网络！'
    },
    lock: {
      unlock: '点击解锁',
      alert: '锁屏密码错误',
      backToLogin: '返回登录',
      entry: '进入系统',
      placeholder: '请输入锁屏密码或者用户密码'
    },
    login: {
      backSignIn: '返回',
      signInFormTitle: '登录',
      ssoFormTitle: '三方授权',
      mobileSignInFormTitle: '手机登录',
      qrSignInFormTitle: '二维码登录',
      signUpFormTitle: '注册',
      forgetFormTitle: '重置密码',
      signInTitle: '杭州嘉程管理系统',
      signInDesc: '输入您的个人详细信息开始使用！',
      policy: '我同意xxx隐私政策',
      scanSign: `扫码后点击"确认"，即可完成登录`,
      loginButton: '登录',
      registerButton: '注册',
      rememberMe: '记住我',
      forgetPassword: '忘记密码?',
      otherSignIn: '其他登录方式',
      // notify
      loginSuccessTitle: '登录成功',
      loginSuccessDesc: '欢迎回来',
      // placeholder
      accountPlaceholder: '请输入账号',
      passwordPlaceholder: '请输入密码',
      smsPlaceholder: '请输入验证码',
      mobilePlaceholder: '请输入手机号码',
      policyPlaceholder: '勾选后才能注册',
      diffPwd: '两次输入密码不一致',
      userName: '账号',
      password: '密码',
      confirmPassword: '确认密码',
      email: '邮箱',
      smsCode: '短信验证码',
      mobile: '手机号码'
    }
  },
  profile: {
    user: {
      title: '个人信息',
      username: '用户名称',
      nickname: '用户昵称',
      mobile: '手机号码',
      email: '用户邮箱',
      dept: '所属部门',
      posts: '所属岗位',
      roles: '所属角色',
      sex: '性别',
      man: '男',
      woman: '女',
      createTime: '创建日期'
    },
    info: {
      title: '基本信息',
      basicInfo: '基本资料',
      resetPwd: '修改密码',
      userSocial: '社交信息'
    },
    rules: {
      nickname: '请输入用户昵称',
      mail: '请输入邮箱地址',
      truemail: '请输入正确的邮箱地址',
      phone: '请输入正确的手机号码',
      truephone: '请输入正确的手机号码'
    },
    password: {
      oldPassword: '旧密码',
      newPassword: '新密码',
      confirmPassword: '确认密码',
      oldPwdMsg: '请输入旧密码',
      newPwdMsg: '请输入新密码',
      cfPwdMsg: '请输入确认密码',
      pwdRules: '长度在 6 到 20 个字符',
      diffPwd: '两次输入密码不一致'
    }
  },
  cropper: {
    selectImage: '选择图片',
    uploadSuccess: '上传成功',
    modalTitle: '头像上传',
    okText: '确认并上传',
    btn_reset: '重置',
    btn_rotate_left: '逆时针旋转',
    btn_rotate_right: '顺时针旋转',
    btn_scale_x: '水平翻转',
    btn_scale_y: '垂直翻转',
    btn_zoom_in: '放大',
    btn_zoom_out: '缩小',
    preview: '预览'
  },
  'OAuth 2.0': 'OAuth 2.0' // 避免菜单名是 OAuth 2.0 时，一直 warn 报错
}
